{"name": "fauzara/zed-pint", "description": "Format code inside Zed editor using <PERSON><PERSON>.", "license": "MIT", "type": "library", "version": "1.0.0", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/support": "^12.17", "laravel/pint": "^1.22", "nyholm/psr7": "^1.8", "psr/http-message": "^2.0", "symfony/process": "^7.3"}, "require-dev": {"brainmaestro/composer-git-hooks": "^3.0", "composer": "*", "ergebnis/composer-normalize": "^2.48", "phpstan/phpstan": "^2.1", "symfony/var-dumper": "^7.3"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Fauzara\\Zed\\Pint\\": "src/"}}, "bin": "bin/zed-pint", "config": {"allow-plugins": {"ergebnis/composer-normalize": true}, "sort-packages": true}, "extra": {"hooks": {"pre-commit": ["composer compile --quiet", "composer compile:add"]}}, "scripts": {"post-install-cmd": ["@hooks:add", "@prettier:config"], "post-update-cmd": ["@hooks:update", "@composer normalize"], "compile": "./compile", "compile:add": "git add bin/zed-pint > /dev/null", "hooks:add": "cghooks add --ignore-lock", "hooks:update": "cghooks update", "lint": "pint -v", "prettier:config": "curl -sSL https://raw.githubusercontent.com/mpietrucha/prettier-config-synchronizer/refs/heads/main/synchronize | bash", "test": ["@test:lint", "@test:types"], "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi"}}
{"directories": ["src", "vendor"], "files": ["composer.json"], "main": "zed-pint", "output": "bin/zed-pint", "compression": "BZ2", "exclude-composer-files": true, "exclude-dev-files": true, "finder": [{"name": "*.php", "exclude": ["tests", "test", "Tests", "Test", "docs", "doc", "examples", "example"], "in": "vendor"}], "exclude": ["vendor/*/tests", "vendor/*/test", "vendor/*/Tests", "vendor/*/Test", "vendor/*/docs", "vendor/*/doc", "vendor/*/examples", "vendor/*/example", "vendor/*/*/tests", "vendor/*/*/test", "vendor/*/*/Tests", "vendor/*/*/Test", "vendor/*/*/docs", "vendor/*/*/doc", "vendor/*/*/examples", "vendor/*/*/example", "vendor/*/README*", "vendor/*/CHANGELOG*", "vendor/*/CONTRIBUTING*", "vendor/*/LICENSE*", "vendor/*/UPGRADE*", "vendor/*/*.md", "vendor/*/*.txt", "vendor/*/*.rst", "vendor/*/phpunit.xml*", "vendor/*/composer.json", "vendor/*/composer.lock", "vendor/*/.git*", "vendor/*/.travis*", "vendor/*/.scrutinizer*", "vendor/*/.php_cs*", "vendor/*/.editorconfig", "vendor/symfony/console", "vendor/symfony/finder", "vendor/symfony/filesystem", "vendor/symfony/string", "vendor/illuminate/collections", "vendor/illuminate/contracts", "vendor/illuminate/macroable", "vendor/illuminate/conditionable", "vendor/nesbot/carbon", "vendor/carbonphp/carbon-doctrine-types", "vendor/doctrine/inflector", "vendor/voku/portable-ascii"], "compactors": ["KevinGH\\Box\\Compactor\\Php", "KevinGH\\Box\\Compactor\\Json", "KevinGH\\Box\\Compactor\\PhpScoper"]}